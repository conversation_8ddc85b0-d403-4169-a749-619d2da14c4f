name: Coverage Monitoring - 覆盖率监控告警

on:
  # 每次主分支有变更时检查覆盖率变化
  push:
    branches: [ main ]
  
  # 每天早上9点检查覆盖率趋势
  schedule:
    - cron: '0 9 * * *'
  
  # 手动触发
  workflow_dispatch:
    inputs:
      alert_threshold:
        description: '告警阈值 (覆盖率下降百分比)'
        required: false
        default: '2'
        type: string

env:
  JAVA_VERSION: '21'
  NODE_VERSION: '20'
  COVERAGE_THRESHOLD: ${{ github.event.inputs.alert_threshold || '2' }}

jobs:
  coverage-baseline:
    name: 建立覆盖率基线
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: testpassword
          POSTGRES_USER: testuser
          POSTGRES_DB: assessment_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: Checkout 代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: 设置 Java ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        cache: maven
    
    - name: 等待数据库就绪
      run: |
        until pg_isready -h localhost -p 5432 -U testuser; do
          echo "等待 PostgreSQL..."
          sleep 2
        done
    
    - name: 运行测试生成覆盖率
      working-directory: ./backend
      run: |
        ./mvnw clean test jacoco:report \
          -Dspring.profiles.active=test \
          -Dspring.datasource.url=************************************************ \
          -Dspring.datasource.username=testuser \
          -Dspring.datasource.password=testpassword \
          -Dspring.redis.host=localhost \
          -Dspring.redis.port=6379
    
    - name: 解析当前覆盖率
      id: current-coverage
      run: |
        if [ -f "./backend/target/site/jacoco/jacoco.csv" ]; then
          # 解析 JaCoCo CSV 获取覆盖率数据
          coverage_data=$(awk -F',' 'NR>1 {
            instruction_missed += $4; instruction_covered += $5;
            branch_missed += $6; branch_covered += $7;
            line_missed += $8; line_covered += $9;
            method_missed += $12; method_covered += $13;
          } END {
            instruction_total = instruction_missed + instruction_covered;
            branch_total = branch_missed + branch_covered;
            line_total = line_missed + line_covered;
            method_total = method_missed + method_covered;
            
            printf "%.2f,%.2f,%.2f,%.2f",
              instruction_covered/instruction_total*100,
              branch_covered/branch_total*100,
              line_covered/line_total*100,
              method_covered/method_total*100;
          }' ./backend/target/site/jacoco/jacoco.csv)
          
          IFS=',' read -r instruction_cov branch_cov line_cov method_cov <<< "$coverage_data"
          
          echo "instruction_coverage=$instruction_cov" >> $GITHUB_OUTPUT
          echo "branch_coverage=$branch_cov" >> $GITHUB_OUTPUT
          echo "line_coverage=$line_cov" >> $GITHUB_OUTPUT
          echo "method_coverage=$method_cov" >> $GITHUB_OUTPUT
          echo "overall_coverage=$instruction_cov" >> $GITHUB_OUTPUT
          
          echo "当前覆盖率: 指令=$instruction_cov%, 分支=$branch_cov%, 行=$line_cov%, 方法=$method_cov%"
        else
          echo "覆盖率文件不存在"
          echo "overall_coverage=0" >> $GITHUB_OUTPUT
        fi
    
    - name: 保存当前覆盖率基线
      run: |
        mkdir -p .github/coverage-history
        
        # 创建覆盖率历史记录
        echo "{
          \"timestamp\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\",
          \"commit\": \"${{ github.sha }}\",
          \"branch\": \"${{ github.ref_name }}\",
          \"instruction_coverage\": ${{ steps.current-coverage.outputs.instruction_coverage }},
          \"branch_coverage\": ${{ steps.current-coverage.outputs.branch_coverage }},
          \"line_coverage\": ${{ steps.current-coverage.outputs.line_coverage }},
          \"method_coverage\": ${{ steps.current-coverage.outputs.method_coverage }},
          \"overall_coverage\": ${{ steps.current-coverage.outputs.overall_coverage }}
        }" > .github/coverage-history/coverage-$(date +%Y%m%d-%H%M%S).json
        
        # 更新最新基线
        echo "{
          \"last_updated\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\",
          \"commit\": \"${{ github.sha }}\",
          \"branch\": \"${{ github.ref_name }}\",
          \"baseline_coverage\": ${{ steps.current-coverage.outputs.overall_coverage }},
          \"instruction_coverage\": ${{ steps.current-coverage.outputs.instruction_coverage }},
          \"branch_coverage\": ${{ steps.current-coverage.outputs.branch_coverage }},
          \"line_coverage\": ${{ steps.current-coverage.outputs.line_coverage }},
          \"method_coverage\": ${{ steps.current-coverage.outputs.method_coverage }}
        }" > .github/coverage-history/baseline.json
    
    outputs:
      current_coverage: ${{ steps.current-coverage.outputs.overall_coverage }}
      instruction_coverage: ${{ steps.current-coverage.outputs.instruction_coverage }}
      branch_coverage: ${{ steps.current-coverage.outputs.branch_coverage }}
      line_coverage: ${{ steps.current-coverage.outputs.line_coverage }}
      method_coverage: ${{ steps.current-coverage.outputs.method_coverage }}

  coverage-trend-analysis:
    name: 覆盖率趋势分析
    runs-on: ubuntu-latest
    needs: coverage-baseline
    if: always()
    
    steps:
    - name: Checkout 代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: 设置 Node.js (用于数据处理)
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
    
    - name: 分析覆盖率趋势
      id: trend-analysis
      run: |
        # 创建覆盖率历史目录
        mkdir -p .github/coverage-history
        
        # 获取历史基线数据
        if [ -f ".github/coverage-history/baseline.json" ]; then
          baseline_coverage=$(jq -r '.baseline_coverage // 0' .github/coverage-history/baseline.json)
        else
          baseline_coverage=0
        fi
        
        current_coverage=${{ needs.coverage-baseline.outputs.current_coverage }}
        
        echo "基线覆盖率: $baseline_coverage%"
        echo "当前覆盖率: $current_coverage%"
        
        # 计算覆盖率变化
        if [ "$baseline_coverage" != "0" ]; then
          coverage_change=$(echo "$current_coverage - $baseline_coverage" | bc -l)
          coverage_change_abs=$(echo "$coverage_change" | sed 's/-//')
          
          echo "覆盖率变化: ${coverage_change}%"
          echo "coverage_change=$coverage_change" >> $GITHUB_OUTPUT
          echo "coverage_change_abs=$coverage_change_abs" >> $GITHUB_OUTPUT
          echo "baseline_coverage=$baseline_coverage" >> $GITHUB_OUTPUT
          
          # 判断是否需要告警
          threshold=${{ env.COVERAGE_THRESHOLD }}
          if (( $(echo "$coverage_change < -$threshold" | bc -l) )); then
            echo "需要告警: 覆盖率下降 ${coverage_change_abs}% > ${threshold}%"
            echo "needs_alert=true" >> $GITHUB_OUTPUT
            echo "alert_type=critical" >> $GITHUB_OUTPUT
          elif (( $(echo "$coverage_change < 0" | bc -l) )); then
            echo "需要警告: 覆盖率轻微下降 ${coverage_change_abs}%"
            echo "needs_alert=true" >> $GITHUB_OUTPUT
            echo "alert_type=warning" >> $GITHUB_OUTPUT
          else
            echo "覆盖率稳定或提升"
            echo "needs_alert=false" >> $GITHUB_OUTPUT
            echo "alert_type=none" >> $GITHUB_OUTPUT
          fi
        else
          echo "首次运行，建立基线"
          echo "needs_alert=false" >> $GITHUB_OUTPUT
          echo "alert_type=baseline" >> $GITHUB_OUTPUT
          echo "coverage_change=0" >> $GITHUB_OUTPUT
        fi
    
    - name: 生成覆盖率趋势报告
      run: |
        echo "# 覆盖率监控报告" > coverage-report.md
        echo "" >> coverage-report.md
        echo "**生成时间**: $(date)" >> coverage-report.md
        echo "**提交**: ${{ github.sha }}" >> coverage-report.md
        echo "**分支**: ${{ github.ref_name }}" >> coverage-report.md
        echo "" >> coverage-report.md
        
        echo "## 📊 当前覆盖率" >> coverage-report.md
        echo "" >> coverage-report.md
        echo "| 类型 | 覆盖率 | 状态 |" >> coverage-report.md
        echo "|------|--------|------|" >> coverage-report.md
        echo "| 指令覆盖率 | ${{ needs.coverage-baseline.outputs.instruction_coverage }}% | ✅ |" >> coverage-report.md
        echo "| 分支覆盖率 | ${{ needs.coverage-baseline.outputs.branch_coverage }}% | ✅ |" >> coverage-report.md
        echo "| 行覆盖率 | ${{ needs.coverage-baseline.outputs.line_coverage }}% | ✅ |" >> coverage-report.md
        echo "| 方法覆盖率 | ${{ needs.coverage-baseline.outputs.method_coverage }}% | ✅ |" >> coverage-report.md
        echo "" >> coverage-report.md
        
        if [ "${{ steps.trend-analysis.outputs.needs_alert }}" = "true" ]; then
          echo "## 🚨 覆盖率告警" >> coverage-report.md
          echo "" >> coverage-report.md
          
          if [ "${{ steps.trend-analysis.outputs.alert_type }}" = "critical" ]; then
            echo "⚠️ **严重告警**: 覆盖率显著下降 ${{ steps.trend-analysis.outputs.coverage_change }}%" >> coverage-report.md
            echo "" >> coverage-report.md
            echo "**基线覆盖率**: ${{ steps.trend-analysis.outputs.baseline_coverage }}%" >> coverage-report.md
            echo "**当前覆盖率**: ${{ needs.coverage-baseline.outputs.current_coverage }}%" >> coverage-report.md
            echo "**下降幅度**: ${{ steps.trend-analysis.outputs.coverage_change_abs }}%" >> coverage-report.md
            echo "" >> coverage-report.md
            echo "### 🎯 紧急行动项" >> coverage-report.md
            echo "1. 立即检查最近的代码变更" >> coverage-report.md
            echo "2. 补充缺失的测试用例" >> coverage-report.md
            echo "3. 阻止覆盖率进一步下降" >> coverage-report.md
          else
            echo "⚠️ **注意**: 覆盖率轻微下降 ${{ steps.trend-analysis.outputs.coverage_change }}%" >> coverage-report.md
            echo "" >> coverage-report.md
            echo "建议关注并考虑补充测试用例" >> coverage-report.md
          fi
        else
          echo "## ✅ 覆盖率状态良好" >> coverage-report.md
          echo "" >> coverage-report.md
          
          if [ "${{ steps.trend-analysis.outputs.alert_type }}" = "baseline" ]; then
            echo "首次建立覆盖率基线: ${{ needs.coverage-baseline.outputs.current_coverage }}%" >> coverage-report.md
          else
            echo "覆盖率保持稳定或有所提升" >> coverage-report.md
            if [ "${{ steps.trend-analysis.outputs.coverage_change }}" != "0" ]; then
              echo "变化: +${{ steps.trend-analysis.outputs.coverage_change }}%" >> coverage-report.md
            fi
          fi
        fi
        
        echo "" >> coverage-report.md
        echo "## 📈 历史趋势" >> coverage-report.md
        echo "" >> coverage-report.md
        
        # 生成最近7天的覆盖率历史
        if [ -d ".github/coverage-history" ]; then
          echo "| 日期 | 覆盖率 | 变化 |" >> coverage-report.md
          echo "|------|--------|------|" >> coverage-report.md
          
          # 列出最近的覆盖率文件
          ls -t .github/coverage-history/coverage-*.json 2>/dev/null | head -7 | while read file; do
            if [ -f "$file" ]; then
              timestamp=$(jq -r '.timestamp // "N/A"' "$file")
              coverage=$(jq -r '.overall_coverage // 0' "$file")
              date_only=$(echo "$timestamp" | cut -d'T' -f1)
              echo "| $date_only | ${coverage}% | - |" >> coverage-report.md
            fi
          done
        else
          echo "暂无历史数据" >> coverage-report.md
        fi
        
        echo "" >> coverage-report.md
        echo "## 🎯 质量目标" >> coverage-report.md
        echo "- **当前目标**: 82% (已达成 ✅)" >> coverage-report.md
        echo "- **长期目标**: 85%" >> coverage-report.md
        echo "- **告警阈值**: 下降超过 ${{ env.COVERAGE_THRESHOLD }}%" >> coverage-report.md
        echo "" >> coverage-report.md
        echo "---" >> coverage-report.md
        echo "*由覆盖率监控系统自动生成*" >> coverage-report.md
    
    - name: 创建或更新覆盖率告警 Issue
      if: steps.trend-analysis.outputs.needs_alert == 'true'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const reportContent = fs.readFileSync('coverage-report.md', 'utf8');
          
          const alertType = '${{ steps.trend-analysis.outputs.alert_type }}';
          const title = alertType === 'critical' 
            ? `🚨 覆盖率严重下降告警 - ${new Date().toISOString().split('T')[0]}`
            : `⚠️ 覆盖率下降提醒 - ${new Date().toISOString().split('T')[0]}`;
          
          const labels = alertType === 'critical' 
            ? ['coverage', 'critical', 'urgent']
            : ['coverage', 'warning'];
          
          // 查找现有的覆盖率告警 Issue
          const issues = await github.rest.issues.listForRepo({
            owner: context.repo.owner,
            repo: context.repo.repo,
            labels: 'coverage',
            state: 'open'
          });
          
          const existingIssue = issues.data.find(issue => 
            issue.title.includes('覆盖率') && issue.title.includes('告警')
          );
          
          const issueBody = `${reportContent}\n\n---\n*此告警由覆盖率监控系统自动创建*`;
          
          if (existingIssue) {
            // 更新现有 Issue
            await github.rest.issues.update({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: existingIssue.number,
              title: title,
              body: issueBody,
              labels: labels
            });
          } else {
            // 创建新 Issue
            await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: title,
              body: issueBody,
              labels: labels
            });
          }
    
    - name: 发送告警通知
      if: steps.trend-analysis.outputs.needs_alert == 'true'
      uses: alibabacloud-actions/dingtalk-notify@v1.0.1
      with:
        url: ${{ secrets.DINGTALK_WEBHOOK }}
        title: "覆盖率告警"
        text: |
          ${{ steps.trend-analysis.outputs.alert_type == 'critical' && '🚨 严重告警' || '⚠️ 覆盖率提醒' }}
          
          📊 智能评估平台覆盖率监控
          
          **基线覆盖率**: ${{ steps.trend-analysis.outputs.baseline_coverage }}%
          **当前覆盖率**: ${{ needs.coverage-baseline.outputs.current_coverage }}%
          **变化幅度**: ${{ steps.trend-analysis.outputs.coverage_change }}%
          
          **分支**: ${{ github.ref_name }}
          **提交**: ${{ github.sha }}
          
          🔗 查看详情: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
    
    - name: 提交覆盖率历史数据
      if: github.ref == 'refs/heads/main'
      run: |
        git config user.name "github-actions[bot]"
        git config user.email "github-actions[bot]@users.noreply.github.com"
        
        git add .github/coverage-history/
        
        if git diff --staged --quiet; then
          echo "无覆盖率变化，跳过提交"
        else
          git commit -m "chore: update coverage baseline

          Coverage: ${{ needs.coverage-baseline.outputs.current_coverage }}%
          
          🤖 Generated with [Claude Code](https://claude.ai/code)
          
          Co-Authored-By: Claude <<EMAIL>>"
          git push
        fi
    
    - name: 上传覆盖率监控报告
      uses: actions/upload-artifact@v4
      with:
        name: coverage-monitoring-report
        path: |
          coverage-report.md
          .github/coverage-history/
        retention-days: 90

  coverage-quality-gates:
    name: 覆盖率质量门禁
    runs-on: ubuntu-latest
    needs: coverage-baseline
    if: github.event_name == 'push'
    
    steps:
    - name: 检查覆盖率质量门禁
      run: |
        current_coverage=${{ needs.coverage-baseline.outputs.current_coverage }}
        min_coverage=80
        target_coverage=85
        
        echo "当前覆盖率: ${current_coverage}%"
        echo "最低要求: ${min_coverage}%"
        echo "目标覆盖率: ${target_coverage}%"
        
        if (( $(echo "$current_coverage < $min_coverage" | bc -l) )); then
          echo "❌ 覆盖率不达标! (${current_coverage}% < ${min_coverage}%)"
          echo "质量门禁阻止合并"
          exit 1
        elif (( $(echo "$current_coverage < $target_coverage" | bc -l) )); then
          echo "⚠️ 覆盖率未达到目标 (${current_coverage}% < ${target_coverage}%)"
          echo "建议继续提升覆盖率"
        else
          echo "✅ 覆盖率达标 (${current_coverage}% >= ${target_coverage}%)"
        fi
    
    - name: 更新覆盖率 Badge
      if: github.ref == 'refs/heads/main'
      run: |
        coverage=${{ needs.coverage-baseline.outputs.current_coverage }}
        
        # 根据覆盖率确定 badge 颜色
        if (( $(echo "$coverage >= 85" | bc -l) )); then
          color="brightgreen"
        elif (( $(echo "$coverage >= 80" | bc -l) )); then
          color="green"
        elif (( $(echo "$coverage >= 70" | bc -l) )); then
          color="yellow"
        else
          color="red"
        fi
        
        echo "生成覆盖率徽章: ${coverage}% ($color)"
        
        # 创建 badge URL
        badge_url="https://img.shields.io/badge/coverage-${coverage}%25-${color}"
        echo "Badge URL: $badge_url"