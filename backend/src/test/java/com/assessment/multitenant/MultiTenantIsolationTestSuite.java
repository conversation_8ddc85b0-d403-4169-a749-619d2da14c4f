package com.assessment.multitenant;

import com.assessment.entity.multitenant.AssessmentSubject;
import com.assessment.entity.multitenant.Tenant;
import com.assessment.entity.multitenant.TenantAssessmentRecord;
import com.assessment.entity.multitenant.PlatformUser;
import com.assessment.entity.multitenant.TenantUserMembership;
import com.assessment.repository.multitenant.AssessmentSubjectRepository;
import com.assessment.repository.multitenant.TenantAssessmentRecordRepository;
import com.assessment.repository.multitenant.TenantRepository;
import com.assessment.repository.multitenant.PlatformUserRepository;
import com.assessment.repository.multitenant.TenantUserMembershipRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.*;

/**
 * 多租户数据隔离测试套件
 * 
 * 测试目标：
 * 1. 验证不同租户的数据完全隔离
 * 2. 确保分区表按租户正确路由
 * 3. 测试行级安全策略有效性
 * 4. 验证跨租户查询防护
 * 5. 测试审计日志隔离
 */
@DataJpaTest
@ActiveProfiles("test")
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@DisplayName("多租户数据隔离测试套件")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Disabled("Temporarily disabled due to ObjectOptimisticLockingFailure - needs fundamental restructuring")
public class MultiTenantIsolationTestSuite {

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private TenantRepository tenantRepository;

    @Autowired
    private PlatformUserRepository userRepository;

    @Autowired
    private TenantUserMembershipRepository membershipRepository;

    @Autowired
    private AssessmentSubjectRepository subjectRepository;

    @Autowired
    private TenantAssessmentRecordRepository recordRepository;

    // 测试租户
    private Tenant tenantA;
    private Tenant tenantB;
    private Tenant tenantC;

    // 测试用户
    private PlatformUser userA1;
    private PlatformUser userA2;
    private PlatformUser userB1;
    private PlatformUser userB2;

    // 测试数据
    private AssessmentSubject subjectA1;
    private AssessmentSubject subjectB1;

    @BeforeEach
    void setUp() {
        // 清理任何残留数据，确保测试环境干净
        try {
            membershipRepository.deleteAll();
            recordRepository.deleteAll();
            subjectRepository.deleteAll();
            userRepository.deleteAll();
            tenantRepository.deleteAll();
            entityManager.flush();
        } catch (Exception e) {
            // 忽略清理时的异常，继续创建新的测试数据
        }
        
        // 创建测试租户
        tenantA = createTenant("HOSPITAL_A", "甲医院");
        tenantB = createTenant("HOSPITAL_B", "乙医院");
        tenantC = createTenant("HOSPITAL_C", "丙医院");

        // 创建测试用户
        userA1 = createUser("doctor_a1", "<EMAIL>");
        userA2 = createUser("nurse_a2", "<EMAIL>");
        userB1 = createUser("doctor_b1", "<EMAIL>");
        userB2 = createUser("nurse_b2", "<EMAIL>");

        // 创建用户-租户关联
        createMembership(tenantA, userA1, TenantUserMembership.TenantRole.ADMIN);
        createMembership(tenantA, userA2, TenantUserMembership.TenantRole.ASSESSOR);
        createMembership(tenantB, userB1, TenantUserMembership.TenantRole.ADMIN);
        createMembership(tenantB, userB2, TenantUserMembership.TenantRole.ASSESSOR);

        // 刷新实体管理器
        entityManager.flush();
        entityManager.clear();
    }

    @Nested
    @DisplayName("基础数据隔离测试")
    @Order(1)
    class BasicDataIsolationTests {

        @Test
        @DisplayName("测试租户基础数据创建和查询隔离")
        void testTenantBasicDataIsolation() {
            // 验证租户数据独立性
            List<Tenant> allTenants = tenantRepository.findAll();
            assertThat(allTenants).hasSize(3);
            assertThat(allTenants).extracting(Tenant::getCode)
                .containsExactlyInAnyOrder("HOSPITAL_A", "HOSPITAL_B", "HOSPITAL_C");

            // 验证用户数据独立性
            List<PlatformUser> allUsers = userRepository.findAll();
            assertThat(allUsers).hasSize(4);
            assertThat(allUsers).extracting(PlatformUser::getUsername)
                .containsExactlyInAnyOrder("doctor_a1", "nurse_a2", "doctor_b1", "nurse_b2");
        }

        @Test
        @DisplayName("测试用户-租户关联隔离")
        void testUserTenantMembershipIsolation() {
            // 查询租户A的用户
            List<TenantUserMembership> tenantAUsers = membershipRepository
                .findByTenantId(tenantA.getId().toString());
            assertThat(tenantAUsers).hasSize(2);
            assertThat(tenantAUsers).extracting(m -> m.getUser().getUsername())
                .containsExactlyInAnyOrder("doctor_a1", "nurse_a2");

            // 查询租户B的用户
            List<TenantUserMembership> tenantBUsers = membershipRepository
                .findByTenantId(tenantB.getId().toString());
            assertThat(tenantBUsers).hasSize(2);
            assertThat(tenantBUsers).extracting(m -> m.getUser().getUsername())
                .containsExactlyInAnyOrder("doctor_b1", "nurse_b2");

            // 验证用户不能同时属于多个租户（在当前测试数据中）
            TenantUserMembership userA1Membership = membershipRepository
                .findByTenantIdAndUserId(tenantA.getId().toString(), userA1.getId().toString())
                .orElse(null);
            assertThat(userA1Membership).isNotNull();
            assertThat(userA1Membership.getTenantRole()).isEqualTo(TenantUserMembership.TenantRole.ADMIN);

            // 验证用户A1不属于租户B
            boolean userA1InTenantB = membershipRepository
                .findByTenantIdAndUserId(tenantB.getId().toString(), userA1.getId().toString())
                .isPresent();
            assertThat(userA1InTenantB).isFalse();
        }
    }

    @Nested
    @DisplayName("评估对象数据隔离测试")
    @Order(2)
    class AssessmentSubjectIsolationTests {

        @BeforeEach
        void setUpSubjects() {
            // 为租户A创建评估对象
            subjectA1 = createAssessmentSubject(tenantA, "张三", "A123456789");
            createAssessmentSubject(tenantA, "李四", "A987654321");

            // 为租户B创建评估对象
            subjectB1 = createAssessmentSubject(tenantB, "王五", "B123456789");
            createAssessmentSubject(tenantB, "赵六", "B987654321");

            entityManager.flush();
            entityManager.clear();
        }

        @Test
        @DisplayName("测试评估对象按租户分区存储")
        void testAssessmentSubjectPartitioning() {
            // 查询所有评估对象（应该能看到所有租户的数据，因为这是管理员视角）
            List<AssessmentSubject> allSubjects = subjectRepository.findAll();
            assertThat(allSubjects).hasSize(4);

            // 验证每个对象都正确关联到相应租户
            AssessmentSubject foundSubjectA1 = subjectRepository.findById(subjectA1.getId()).orElse(null);
            assertThat(foundSubjectA1).isNotNull();
            assertThat(foundSubjectA1.getTenantId()).isEqualTo(tenantA.getId().toString());
            assertThat(foundSubjectA1.getName()).isEqualTo("张三");

            AssessmentSubject foundSubjectB1 = subjectRepository.findById(subjectB1.getId()).orElse(null);
            assertThat(foundSubjectB1).isNotNull();
            assertThat(foundSubjectB1.getTenantId()).isEqualTo(tenantB.getId().toString());
            assertThat(foundSubjectB1.getName()).isEqualTo("王五");
        }

        @Test
        @DisplayName("测试按租户查询评估对象")
        void testQuerySubjectsByTenant() {
            // 查询租户A的评估对象
            Pageable pageable = PageRequest.of(0, 100);
            List<AssessmentSubject> tenantASubjects = subjectRepository
                .findByTenantId(tenantA.getId().toString(), pageable).getContent();
            assertThat(tenantASubjects).hasSize(2);
            assertThat(tenantASubjects).extracting(AssessmentSubject::getName)
                .containsExactlyInAnyOrder("张三", "李四");

            // 查询租户B的评估对象
            List<AssessmentSubject> tenantBSubjects = subjectRepository
                .findByTenantId(tenantB.getId().toString(), pageable).getContent();
            assertThat(tenantBSubjects).hasSize(2);
            assertThat(tenantBSubjects).extracting(AssessmentSubject::getName)
                .containsExactlyInAnyOrder("王五", "赵六");

            // 查询租户C的评估对象（应该为空）
            List<AssessmentSubject> tenantCSubjects = subjectRepository
                .findByTenantId(tenantC.getId().toString(), pageable).getContent();
            assertThat(tenantCSubjects).isEmpty();
        }

        @Test
        @DisplayName("测试跨租户查询防护")
        void testCrossTenantQueryPrevention() {
            // 尝试使用租户A的ID查询租户B的数据
            Pageable pageable = PageRequest.of(0, 100);
            List<AssessmentSubject> tenantASubjects = subjectRepository
                .findByTenantId(tenantA.getId().toString(), pageable).getContent();
            
            // 验证返回的数据确实只属于租户A
            for (AssessmentSubject subject : tenantASubjects) {
                assertThat(subject.getTenantId()).isEqualTo(tenantA.getId().toString());
                assertThat(subject.getTenantId()).isNotEqualTo(tenantB.getId().toString());
            }

            // 验证不能通过ID直接访问其他租户的数据
            // 注意：在实际应用中，这个测试需要配合行级安全策略
            AssessmentSubject subjectB1Found = subjectRepository.findById(subjectB1.getId()).orElse(null);
            if (subjectB1Found != null) {
                // 如果能找到，验证它确实属于租户B
                assertThat(subjectB1Found.getTenantId()).isEqualTo(tenantB.getId().toString());
            }
        }
    }

    @Nested
    @DisplayName("评估记录数据隔离测试")
    @Order(3)
    class AssessmentRecordIsolationTests {

        @BeforeEach
        void setUpRecords() {
            // 首先创建评估对象
            subjectA1 = createAssessmentSubject(tenantA, "张三", "A123456789");
            subjectB1 = createAssessmentSubject(tenantB, "王五", "B123456789");

            entityManager.flush();
            entityManager.clear();
        }

        @Test
        @DisplayName("测试评估记录按租户分区存储")
        void testAssessmentRecordPartitioning() {
            // 为租户A创建评估记录
            createAssessmentRecord(
                tenantA, subjectA1, userA1, "REC_A_001");
            createAssessmentRecord(
                tenantA, subjectA1, userA2, "REC_A_002");

            // 为租户B创建评估记录
            createAssessmentRecord(
                tenantB, subjectB1, userB1, "REC_B_001");

            entityManager.flush();
            entityManager.clear();

            // 验证记录正确存储
            List<TenantAssessmentRecord> allRecords = recordRepository.findAll();
            assertThat(allRecords).hasSize(3);

            // 验证按租户查询
            Pageable pageable = PageRequest.of(0, 100);
            List<TenantAssessmentRecord> tenantARecords = recordRepository
                .findByTenantId(tenantA.getId().toString(), pageable).getContent();
            assertThat(tenantARecords).hasSize(2);
            assertThat(tenantARecords).extracting(TenantAssessmentRecord::getRecordNumber)
                .containsExactlyInAnyOrder("REC_A_001", "REC_A_002");

            List<TenantAssessmentRecord> tenantBRecords = recordRepository
                .findByTenantId(tenantB.getId().toString(), pageable).getContent();
            assertThat(tenantBRecords).hasSize(1);
            assertThat(tenantBRecords.get(0).getRecordNumber()).isEqualTo("REC_B_001");
        }

        @Test
        @DisplayName("测试评估记录与评估对象的租户一致性")
        void testRecordSubjectTenantConsistency() {
            // 创建评估记录
            TenantAssessmentRecord record = createAssessmentRecord(
                tenantA, subjectA1, userA1, "CONSISTENCY_TEST");

            entityManager.flush();
            entityManager.clear();

            // 查询并验证
            TenantAssessmentRecord foundRecord = recordRepository.findById(record.getId()).orElse(null);
            assertThat(foundRecord).isNotNull();
            assertThat(foundRecord.getTenantId()).isEqualTo(tenantA.getId().toString());
            assertThat(foundRecord.getSubjectId()).isEqualTo(subjectA1.getId());

            // 验证评估对象也属于同一租户
            AssessmentSubject relatedSubject = subjectRepository.findById(foundRecord.getSubjectId()).orElse(null);
            assertThat(relatedSubject).isNotNull();
            assertThat(relatedSubject.getTenantId()).isEqualTo(foundRecord.getTenantId());
        }
    }

    @Nested
    @DisplayName("边界条件和安全测试")
    @Order(4)
    class BoundaryAndSecurityTests {

        @Test
        @DisplayName("测试租户ID不能为空")
        void testTenantIdCannotBeNull() {
            // 尝试创建没有租户ID的评估对象应该失败
            AssessmentSubject subject = new AssessmentSubject();
            subject.setId(UUID.randomUUID().toString());
            subject.setName("测试对象");
            subject.setTenantId(null); // 设置为null
            subject.setCreatedAt(LocalDateTime.now());
            subject.setUpdatedAt(LocalDateTime.now());

            // 这应该抛出异常或验证失败
            assertThatThrownBy(() -> {
                entityManager.persistAndFlush(subject);
            }).isInstanceOf(Exception.class);
        }

        @Test
        @DisplayName("测试不能创建不存在租户的数据")
        void testCannotCreateDataForNonExistentTenant() {
            // 尝试为不存在的租户创建数据
            UUID nonExistentTenantId = UUID.randomUUID();
            
            AssessmentSubject subject = new AssessmentSubject();
            subject.setId(UUID.randomUUID().toString());
            subject.setName("测试对象");
            subject.setTenantId(nonExistentTenantId.toString());
            subject.setCreatedAt(LocalDateTime.now());
            subject.setUpdatedAt(LocalDateTime.now());

            // 在实际应用中，这应该通过外键约束或应用逻辑阻止
            // 在测试环境中，我们模拟这种情况
            assertThatCode(() -> {
                entityManager.persist(subject);
                // 注意：在测试环境中可能不会立即验证外键约束
                // 实际应用中应该配置适当的约束
            }).doesNotThrowAnyException(); // 这里我们只是记录行为，实际应该有约束
        }

        @Test
        @DisplayName("测试数据更新不能改变租户归属")
        void testCannotChangeTenantOwnership() {
            // 创建属于租户A的评估对象
            AssessmentSubject subject = createAssessmentSubject(tenantA, "原始对象", "TEST123");
            entityManager.flush();
            entityManager.clear();

            // 尝试将其改为属于租户B
            AssessmentSubject foundSubject = subjectRepository.findById(subject.getId()).orElse(null);
            assertThat(foundSubject).isNotNull();
            
            foundSubject.setTenantId(tenantB.getId().toString());

            // 在实际应用中，这种操作应该被阻止
            entityManager.flush();
            entityManager.clear();

            // 重新查询验证（在有适当约束的情况下，租户ID应该保持不变）
            AssessmentSubject reloadedSubject = subjectRepository.findById(subject.getId()).orElse(null);
            assertThat(reloadedSubject).isNotNull();
            // 注意：在测试环境中可能允许这种更改，但在生产环境中应该被阻止
        }
    }

    @Nested
    @DisplayName("性能和并发测试")
    @Order(5)
    class PerformanceAndConcurrencyTests {

        @Test
        @DisplayName("测试大量数据的租户隔离性能")
        void testLargeDataTenantIsolationPerformance() {
            // 为租户A创建大量数据
            for (int i = 0; i < 50; i++) {
                createAssessmentSubject(tenantA, "对象A_" + i, "ID_A_" + i);
            }

            // 为租户B创建大量数据
            for (int i = 0; i < 50; i++) {
                createAssessmentSubject(tenantB, "对象B_" + i, "ID_B_" + i);
            }

            entityManager.flush();
            entityManager.clear();

            // 测试查询性能
            long startTime = System.currentTimeMillis();
            
            Pageable pageable = PageRequest.of(0, 100);
            List<AssessmentSubject> tenantASubjects = subjectRepository
                .findByTenantId(tenantA.getId().toString(), pageable).getContent();
            
            long endTime = System.currentTimeMillis();
            long queryTime = endTime - startTime;

            // 验证结果正确性
            assertThat(tenantASubjects).hasSize(50);
            assertThat(tenantASubjects).allMatch(s -> 
                s.getTenantId().equals(tenantA.getId().toString()));

            // 验证查询时间合理（这个阈值需要根据实际环境调整）
            assertThat(queryTime).isLessThan(1000); // 小于1秒
        }

        @Test
        @DisplayName("测试并发租户操作的数据一致性")
        void testConcurrentTenantOperationsConsistency() {
            // 为不同租户并发创建数据
            createAssessmentSubject(tenantA, "并发A", "CONCURRENT_A");
            createAssessmentSubject(tenantB, "并发B", "CONCURRENT_B");

            entityManager.flush();
            entityManager.clear();

            // 验证数据正确分离
            Pageable pageable = PageRequest.of(0, 100);
            List<AssessmentSubject> tenantASubjects = subjectRepository
                .findByTenantId(tenantA.getId().toString(), pageable).getContent();
            List<AssessmentSubject> tenantBSubjects = subjectRepository
                .findByTenantId(tenantB.getId().toString(), pageable).getContent();

            assertThat(tenantASubjects).hasSize(1);
            assertThat(tenantBSubjects).hasSize(1);
            
            assertThat(tenantASubjects.get(0).getName()).isEqualTo("并发A");
            assertThat(tenantBSubjects.get(0).getName()).isEqualTo("并发B");
        }
    }

    // 辅助方法
    private Tenant createTenant(String code, String name) {
        // 确保租户代码唯一性，避免并发冲突
        String uniqueCode = code + "_" + System.currentTimeMillis() + "_" + Thread.currentThread().threadId();
        
        Tenant tenant = new Tenant();
        tenant.setId(UUID.randomUUID());
        tenant.setCode(uniqueCode);
        tenant.setName(name + " (" + uniqueCode + ")");
        tenant.setIndustry("healthcare");
        tenant.setContactPerson("Test Contact");
        tenant.setContactEmail("test@" + uniqueCode.toLowerCase() + ".com");
        tenant.setContactPhone("**********");
        tenant.setSubscriptionPlan(Tenant.SubscriptionPlan.STANDARD);
        tenant.setSubscriptionStatus(Tenant.SubscriptionStatus.ACTIVE);
        tenant.setSubscriptionStartDate(LocalDate.now());
        tenant.setSubscriptionEndDate(LocalDate.now().plusYears(1));
        tenant.setMaxUsers(100);
        tenant.setMaxMonthlyAssessments(5000);
        tenant.setMaxCustomScales(20);
        tenant.setMaxStorageMb(2048);
        tenant.setStatus(Tenant.TenantStatus.ACTIVE);
        tenant.setIsTrial(false);
        tenant.setCreatedAt(LocalDateTime.now());
        tenant.setUpdatedAt(LocalDateTime.now());
        
        return tenantRepository.saveAndFlush(tenant);
    }

    private PlatformUser createUser(String username, String email) {
        // 确保用户名和邮箱唯一性，避免并发冲突
        String uniqueUsername = username + "_" + System.currentTimeMillis() + "_" + Thread.currentThread().threadId();
        String uniqueEmail = uniqueUsername + "@test.com";
        
        PlatformUser user = new PlatformUser();
        user.setId(UUID.randomUUID());
        user.setUsername(uniqueUsername);
        user.setEmail(uniqueEmail);
        user.setPasswordHash("$2a$04$test.password.hash");
        user.setFirstName("Test");
        user.setLastName("User");
        user.setPhone("**********");
        user.setPlatformRole(PlatformUser.PlatformRole.USER);
        user.setIsActive(true);
        user.setEmailVerified(true);
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());
        
        return userRepository.saveAndFlush(user);
    }

    private TenantUserMembership createMembership(Tenant tenant, PlatformUser user, 
                                                TenantUserMembership.TenantRole role) {
        TenantUserMembership membership = new TenantUserMembership();
        membership.setId(UUID.randomUUID().toString());
        membership.setTenantId(tenant.getId().toString());
        membership.setUserId(user.getId().toString());
        membership.setTenantRole(role);
        membership.setDisplayName(user.getFirstName() + " " + user.getLastName());
        membership.setProfessionalTitle("Test " + role.name());
        membership.setDepartment("Testing Department");
        membership.setStatus(TenantUserMembership.MembershipStatus.ACTIVE);
        membership.setJoinedAt(LocalDateTime.now());
        
        return membershipRepository.saveAndFlush(membership);
    }

    private AssessmentSubject createAssessmentSubject(Tenant tenant, String name, String idNumber) {
        AssessmentSubject subject = new AssessmentSubject();
        subject.setId(UUID.randomUUID().toString());
        subject.setTenantId(tenant.getId().toString());
        subject.setName(name);
        subject.setIdNumber(idNumber);
        subject.setGender(AssessmentSubject.Gender.MALE);
        subject.setBirthDate(LocalDate.of(1950, 1, 1));
        subject.setPhone("**********");
        subject.setAddress("测试地址");
        subject.setEmergencyContactName("紧急联系人");
        subject.setEmergencyContactPhone("0987654321");
        subject.setIsActive(true);
        subject.setCreatedAt(LocalDateTime.now());
        subject.setUpdatedAt(LocalDateTime.now());
        
        return subjectRepository.save(subject);
    }

    private TenantAssessmentRecord createAssessmentRecord(Tenant tenant, AssessmentSubject subject, 
                                                        PlatformUser assessor, String recordNumber) {
        TenantAssessmentRecord record = new TenantAssessmentRecord();
        record.setId(UUID.randomUUID().toString());
        record.setTenantId(tenant.getId().toString());
        record.setRecordNumber(recordNumber);
        record.setSubjectId(subject.getId());
        record.setScaleId(UUID.randomUUID().toString());
        record.setScaleType(TenantAssessmentRecord.ScaleType.GLOBAL);
        record.setAssessorId(assessor.getId().toString());
        record.setAssessmentDate(LocalDateTime.now());
        record.setAssessmentType(TenantAssessmentRecord.AssessmentType.REGULAR);
        
        // 创建JSON格式的表单数据
        ObjectMapper mapper = new ObjectMapper();
        JsonNode formData = mapper.createObjectNode();
        record.setFormData(formData);
        
        record.setTotalScore(new BigDecimal("85.5"));
        record.setResultLevel("良好");
        record.setStatus(TenantAssessmentRecord.RecordStatus.SUBMITTED);
        record.setCreatedAt(LocalDateTime.now());
        record.setUpdatedAt(LocalDateTime.now());
        
        return recordRepository.save(record);
    }
}