<template>
  <el-dialog
    v-model="visible"
    title="安全验证"
    :width="400"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    center
    @close="handleClose"
  >
    <div class="captcha-container">
      <!-- 验证码内容 -->
      <div v-if="captchaData.originalImageBase64" class="slider-captcha">
        <!-- 背景图片容器 -->
        <div 
          class="captcha-image-panel" 
          :style="{width: imgWidth + 'px', height: imgHeight + 'px'}"
        >
          <!-- 背景图片 -->
          <img 
            :src="'data:image/png;base64,' + captchaData.originalImageBase64" 
            class="captcha-bg-image"
            draggable="false"
          />
          
          <!-- 滑块图片 -->
          <div 
            v-if="captchaData.jigsawImageBase64"
            class="captcha-block"
            :style="{
              left: blockLeft + 'px',
              top: '0px',
              width: blockWidth + 'px',
              height: imgHeight + 'px'
            }"
          >
            <img 
              :src="'data:image/png;base64,' + captchaData.jigsawImageBase64"
              class="captcha-block-image"
              draggable="false"
            />
          </div>

          <!-- 刷新按钮 -->
          <el-button 
            class="captcha-refresh" 
            @click="refreshCaptcha"
            :icon="Refresh"
            circle
            size="small"
          />

          <!-- 提示信息 -->
          <transition name="tip-fade">
            <div v-if="tipMessage" class="captcha-tip" :class="verifySuccess ? 'tip-success' : 'tip-error'">
              {{ tipMessage }}
            </div>
          </transition>
        </div>

        <!-- 滑动条 -->
        <div class="captcha-slider" :style="{width: imgWidth + 'px'}">
          <!-- 滑动轨道 -->
          <div class="slider-track">
            <div class="slider-track-bg">
              <span class="slider-text">{{ sliderText }}</span>
            </div>
            
            <!-- 已滑动区域 -->
            <div 
              class="slider-fill" 
              :style="{
                width: sliderLeft + 'px',
                transition: isMoving ? 'none' : 'all 0.3s ease'
              }"
            >
              <span class="slider-text">{{ finishText }}</span>
            </div>
          </div>

          <!-- 滑块 -->
          <div 
            class="slider-button"
            :style="{
              left: sliderLeft + 'px',
              transition: isMoving ? 'none' : 'all 0.3s ease'
            }"
            @mousedown="handleMouseDown"
          >
            <el-icon class="slider-icon" :class="verifySuccess ? 'icon-success' : 'icon-normal'">
              <Check v-if="verifySuccess" />
              <ArrowRight v-else />
            </el-icon>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-else class="captcha-loading">
        <el-loading-indicator />
        <p>验证码加载中...</p>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="refreshCaptcha" :icon="Refresh">
          刷新验证码
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onBeforeUnmount, withDefaults } from 'vue'
import { Refresh, Check, ArrowRight } from '@element-plus/icons-vue'
import { getCaptcha, checkCaptcha } from '@/api/captcha'

// 类型定义
interface CaptchaData {
  token: string
  originalImageBase64: string
  jigsawImageBase64: string
  secretKey: string
  result: boolean
}

interface CaptchaVerifyData {
  token: string
  verification: string
}

// Props
const props = withDefaults(defineProps<{
  modelValue?: boolean
}>(), {
  modelValue: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'verify-success': [data: CaptchaVerifyData]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 验证码数据
const captchaData = reactive<CaptchaData>({
  token: '',
  originalImageBase64: '',
  jigsawImageBase64: '',
  secretKey: '',
  result: false
})

// 图片尺寸配置
const imgWidth = 310
const imgHeight = 155
const blockWidth = 47

// 滑块状态
const sliderLeft = ref(0)
const blockLeft = ref(0)
const isMoving = ref(false)
const startX = ref(0)
const verifySuccess = ref(false)

// 文本提示
const sliderText = ref('向右滑动完成验证')
const finishText = ref('验证成功')
const tipMessage = ref('')

// 验证结果
const isVerified = ref(false)

// 监听弹窗显示状态
watch(visible, (newVal) => {
  if (newVal) {
    initCaptcha()
  } else {
    resetCaptcha()
  }
})

// 初始化验证码
const initCaptcha = async () => {
  try {
    const response = await getCaptcha()
    if (response.success && response.data) {
      Object.assign(captchaData, response.data)
      resetSlider()
    } else {
      showTip('验证码加载失败', false)
    }
  } catch (error) {
    console.error('获取验证码失败:', error)
    showTip('验证码加载失败', false)
  }
}

// 刷新验证码
const refreshCaptcha = async () => {
  resetCaptcha()
  await initCaptcha()
}

// 重置验证码状态
const resetCaptcha = () => {
  Object.assign(captchaData, {
    token: '',
    originalImageBase64: '',
    jigsawImageBase64: '',
    secretKey: '',
    result: false
  })
  resetSlider()
  tipMessage.value = ''
  verifySuccess.value = false
  isVerified.value = false
}

// 重置滑块状态
const resetSlider = () => {
  sliderLeft.value = 0
  blockLeft.value = 0
  isMoving.value = false
  verifySuccess.value = false
}

// 鼠标按下
const handleMouseDown = (e: MouseEvent) => {
  if (verifySuccess.value) return
  
  isMoving.value = true
  startX.value = e.clientX
  tipMessage.value = ''
  
  // 添加全局鼠标事件监听
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  
  // 防止文本选择
  e.preventDefault()
}

// 鼠标移动
const handleMouseMove = (e: MouseEvent) => {
  if (!isMoving.value || verifySuccess.value) return

  const deltaX = e.clientX - startX.value
  
  // 计算滑块位置，限制在有效范围内
  const maxSlideDistance = imgWidth - 40 // 滑块宽度40px
  const newSliderLeft = Math.max(0, Math.min(deltaX, maxSlideDistance))
  
  sliderLeft.value = newSliderLeft
  blockLeft.value = newSliderLeft
}

// 鼠标松开
const handleMouseUp = async () => {
  if (!isMoving.value || verifySuccess.value) return
  
  isMoving.value = false
  
  // 移除全局事件监听
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)

  // 验证滑动距离
  try {
    const verifyData = {
      captchaType: 'blockPuzzle',
      token: captchaData.token,
      pointJson: JSON.stringify({
        x: Math.round(sliderLeft.value),
        y: 5
      }),
      verification: captchaData.secretKey
    }

    const response = await checkCaptcha(verifyData)
    
    if (response.success && response.data && response.data.result) {
      verifySuccess.value = true
      isVerified.value = true
      showTip('验证成功', true)
      
      // 延迟关闭弹窗并触发成功回调
      setTimeout(() => {
        emit('verify-success', {
          token: captchaData.token,
          verification: captchaData.secretKey
        })
        handleClose()
      }, 1000)
      
    } else {
      showTip('验证失败，请重试', false)
      resetSlider()
    }
  } catch (error) {
    console.error('验证失败:', error)
    showTip('验证异常，请重试', false)
    resetSlider()
  }
}

// 显示提示信息
const showTip = (message: string, isSuccess: boolean) => {
  tipMessage.value = message
  verifySuccess.value = isSuccess
  
  // 3秒后清除提示
  setTimeout(() => {
    if (!isSuccess) {
      tipMessage.value = ''
    }
  }, 3000)
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}

// 组件卸载时清理事件监听
onBeforeUnmount(() => {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
})
</script>

<style scoped>
.captcha-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 0;
}

.slider-captcha {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.captcha-image-panel {
  position: relative;
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e9ecef;
  background: #f8f9fa;
}

.captcha-bg-image {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.captcha-block {
  position: absolute;
  top: 0;
  z-index: 2;
}

.captcha-block-image {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.85) contrast(0.9);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.captcha-refresh {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 3;
}

.captcha-tip {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  color: #ffffff;
  z-index: 3;
  white-space: nowrap;
}

.tip-success {
  background: #52c41a;
}

.tip-error {
  background: #ff4d4f;
}

.tip-fade-enter-active,
.tip-fade-leave-active {
  transition: opacity 0.3s ease;
}

.tip-fade-enter-from,
.tip-fade-leave-to {
  opacity: 0;
}

.captcha-slider {
  width: 100%;
  height: 40px;
  position: relative;
  background: #f5f5f5;
  border-radius: 20px;
  overflow: hidden;
}

.slider-track {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  overflow: hidden;
}

.slider-track-bg {
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #d9d9d9;
  border-radius: 20px;
}

.slider-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #52c41a, #73d13d);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slider-text {
  font-size: 14px;
  color: #999999;
  user-select: none;
  pointer-events: none;
}

.slider-fill .slider-text {
  color: #ffffff;
}

.slider-button {
  position: absolute;
  top: 0;
  left: 0;
  width: 40px;
  height: 40px;
  background: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 2;
  user-select: none;
}

.slider-button:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.slider-icon {
  font-size: 16px;
  transition: all 0.3s ease;
}

.icon-normal {
  color: #999999;
}

.icon-success {
  color: #52c41a;
}

.captcha-loading {
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999999;
  gap: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>