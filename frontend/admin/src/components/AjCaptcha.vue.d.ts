declare module '@/components/AjCaptcha.vue' {
  import { DefineComponent } from 'vue'
  
  interface CaptchaVerifyData {
    token: string
    verification: string
  }
  
  const AjCaptcha: DefineComponent<{
    modelValue: {
      type: boolean
      default: false
    }
  }, {}, {}, {}, {}, {}, {}, {
    'update:modelValue': (value: boolean) => void
    'verify-success': (data: CaptchaVerifyData) => void
  }>
  
  export default AjCaptcha
}