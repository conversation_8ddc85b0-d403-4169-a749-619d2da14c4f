<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
    <div class="max-w-md w-full bg-white rounded-2xl shadow-xl p-8">
      <div class="text-center mb-8">
        <div class="mx-auto h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center mb-4">
          <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
          </svg>
        </div>
        <h2 class="text-2xl font-bold text-gray-900">智能评估平台</h2>
        <p class="text-gray-600 mt-2">安全登录系统</p>
      </div>

      <!-- 登录表单 -->
      <el-form :model="loginForm" :rules="rules" ref="loginFormRef" @submit.prevent="handleLogin" class="space-y-4">
        <el-form-item prop="username">
          <label class="block text-sm font-medium text-gray-700 mb-2">用户名</label>
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名，如：zhangsan.001"
            size="large"
            prefix-icon="User"
            clearable
          />
          <div class="text-xs text-gray-500 mt-1">
            格式：姓名拼音.工号 (如：zhangsan.001)
          </div>
        </el-form-item>
        
        <el-form-item prop="tenantCode">
          <label class="block text-sm font-medium text-gray-700 mb-2">机构代码</label>
          <el-input
            v-model="loginForm.tenantCode"
            placeholder="请输入机构代码，如：SH02YL01"
            size="large"
            prefix-icon="Building"
            clearable
            style="text-transform: uppercase"
            @input="handleTenantCodeInput"
          />
          <!-- 智能提示 -->
          <div v-if="tenantCodeHint" class="text-xs mt-1" :class="{
            'text-green-600': tenantCodeHint.startsWith('✓'),
            'text-blue-600': tenantCodeHint.startsWith('💡'),
            'text-red-600': tenantCodeHint.startsWith('⚠️')
          }">
            {{ tenantCodeHint }}
          </div>
          <!-- 静态帮助信息 -->
          <div v-else class="text-xs text-gray-500 mt-1">
            <div>省级：SH01MZ (上海民政厅) · 市级：SH02MZ (浦东民政局)</div>
            <div>机构：SH02YL01 (浦东阳光养老院) · 系统：SYSTEM</div>
          </div>
        </el-form-item>
        
        <el-form-item prop="password">
          <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <!-- 验证码状态提示 -->
        <div class="captcha-status" v-if="captchaVerified">
          <span class="status-icon">✅</span>
          <span class="status-text">滑动验证码已通过</span>
        </div>
        <div class="captcha-status warning" v-else>
          <span class="status-icon">🔒</span>
          <span class="status-text">请完成下方滑动验证码</span>
        </div>
        
        <!-- 内嵌滑动验证码 -->
        <div v-if="!captchaVerified && captchaData.originalImageBase64" class="inline-captcha">
          <!-- 背景图片容器 -->
          <div 
            class="captcha-image-panel" 
            :style="{width: imgWidth + 'px', height: imgHeight + 'px'}"
          >
            <!-- 背景图片 -->
            <img 
              :src="'data:image/png;base64,' + captchaData.originalImageBase64" 
              class="captcha-bg-image"
              draggable="false"
            />
            
            <!-- 滑块图片 -->
            <div 
              v-if="captchaData.jigsawImageBase64"
              class="captcha-block"
              :style="{
                left: blockLeft + 'px',
                top: '0px',
                width: blockWidth + 'px',
                height: imgHeight + 'px'
              }"
            >
              <img 
                :src="'data:image/png;base64,' + captchaData.jigsawImageBase64"
                class="captcha-block-image"
                draggable="false"
              />
            </div>

            <!-- 刷新按钮 -->
            <el-button 
              class="captcha-refresh" 
              @click="refreshCaptcha"
              :icon="Refresh"
              circle
              size="small"
            />

            <!-- 提示信息 -->
            <transition name="tip-fade">
              <div v-if="tipMessage" class="captcha-tip" :class="verifySuccess ? 'tip-success' : 'tip-error'">
                {{ tipMessage }}
              </div>
            </transition>
          </div>

          <!-- 滑动条 -->
          <div class="captcha-slider" :style="{width: imgWidth + 'px'}">
            <!-- 滑动轨道 -->
            <div class="slider-track">
              <div class="slider-track-bg">
                <span class="slider-text">{{ sliderText }}</span>
              </div>
              
              <!-- 已滑动区域 -->
              <div 
                class="slider-fill" 
                :style="{
                  width: sliderLeft + 'px',
                  transition: isMoving ? 'none' : 'all 0.3s ease'
                }"
              >
                <span class="slider-text">{{ finishText }}</span>
              </div>
            </div>

            <!-- 滑块 -->
            <div 
              class="slider-button"
              :style="{
                left: sliderLeft + 'px',
                transition: isMoving ? 'none' : 'all 0.3s ease'
              }"
              @mousedown="handleMouseDown"
            >
              <el-icon class="slider-icon" :class="verifySuccess ? 'icon-success' : 'icon-normal'">
                <Check v-if="verifySuccess" />
                <ArrowRight v-else />
              </el-icon>
            </div>
          </div>
        </div>
        
        <el-button
          type="primary"
          size="large"
          :loading="loading"
          :disabled="loading || !captchaVerified"
          @click="handleLogin"
          class="w-full"
          :class="{ 'disabled-button': !captchaVerified }"
        >
          <span v-if="loading">登录中...</span>
          <span v-else-if="!captchaVerified">请先完成验证码</span>
          <span v-else>安全登录</span>
        </el-button>
      </el-form>

      <!-- 帮助链接 -->
      <div class="mt-6 text-center">
        <el-button link @click="showHelpDialog = true" class="text-sm text-gray-500">
          登录遇到问题？
        </el-button>
      </div>
    </div>

    <!-- 帮助对话框 -->
    <el-dialog v-model="showHelpDialog" title="登录帮助" width="400px">
      <div class="space-y-4 text-sm">
        <div>
          <h4 class="font-medium text-gray-900 mb-2">用户名格式：</h4>
          <ul class="list-disc list-inside text-gray-600 space-y-1">
            <li>姓名拼音.工号：zhangsan.001</li>
            <li>姓名拼音.部门：lisi.asm (assessor)</li>
            <li>姓名拼音.角色：wangwu.admin</li>
          </ul>
        </div>
        
        <div>
          <h4 class="font-medium text-gray-900 mb-2">机构代码规则：</h4>
          <ul class="list-disc list-inside text-gray-600 space-y-1">
            <li>省级：SH01MZ (上海01民政)</li>
            <li>市级：SH02MZ (上海02民政)</li>
            <li>机构：SH02YL01 (上海02养老01)</li>
            <li>系统：SYSTEM (超级管理员)</li>
          </ul>
        </div>
        
        <div>
          <h4 class="font-medium text-gray-900 mb-2">常见问题：</h4>
          <ul class="list-disc list-inside text-gray-600 space-y-1">
            <li>忘记机构代码：联系机构管理员</li>
            <li>用户名错误：检查拼音和工号格式</li>
            <li>无法登录：确认账号状态和权限</li>
          </ul>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showHelpDialog = false">知道了</el-button>
      </template>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import request from '@/utils/request';
import { Refresh, Check, ArrowRight } from '@element-plus/icons-vue';
import { getCaptcha, checkCaptcha } from '@/api/captcha';

const router = useRouter();
const loading = ref(false);
const loginFormRef = ref();
const showHelpDialog = ref(false);

// 验证码相关
const captchaToken = ref('');
const captchaVerification = ref('');
const captchaVerified = ref(false);  // 验证码是否已通过

// 验证码数据
const captchaData = reactive({
  token: '',
  originalImageBase64: '',
  jigsawImageBase64: '',
  secretKey: '',
  result: false
});

// 图片尺寸配置
const imgWidth = 310;
const imgHeight = 155;
const blockWidth = 47;

// 滑块状态
const sliderLeft = ref(0);
const blockLeft = ref(0);
const isMoving = ref(false);
const startX = ref(0);
const verifySuccess = ref(false);

// 文本提示
const sliderText = ref('向右滑动完成验证');
const finishText = ref('验证成功');
const tipMessage = ref('');

const loginForm = reactive({
  username: '',
  tenantCode: '',
  password: '',
});

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { pattern: /^[a-zA-Z]+\.[a-zA-Z0-9]+$/, message: '用户名格式：姓名拼音.工号，如：zhangsan.001', trigger: 'blur' }
  ],
  tenantCode: [
    { required: true, message: '请输入机构代码', trigger: 'blur' },
    { min: 3, message: '机构代码至少3位', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
  ],
};

// 机构代码映射表类型定义
interface TenantInfo {
  name: string;
  type: string;
  level: string;
}

// 机构代码映射表（用于验证和提示）
const tenantCodeMap: Record<string, TenantInfo> = {
  // 系统管理员
  'SYSTEM': { name: '系统平台', type: 'PLATFORM', level: '系统级' },
  'MAINT': { name: '系统维护', type: 'MAINTENANCE', level: '系统级' },
  
  // 省级机构
  'SH01MZ': { name: '上海市民政厅', type: 'GOVERNMENT', level: '省级' },
  'BJ01MZ': { name: '北京市民政厅', type: 'GOVERNMENT', level: '省级' },
  'GD01MZ': { name: '广东省民政厅', type: 'GOVERNMENT', level: '省级' },
  
  // 市级机构
  'SH02MZ': { name: '上海市浦东新区民政局', type: 'GOVERNMENT', level: '市级' },
  'SH03MZ': { name: '上海市徐汇区民政局', type: 'GOVERNMENT', level: '市级' },
  'BJ02MZ': { name: '北京市朝阳区民政局', type: 'GOVERNMENT', level: '市级' },
  
  // 养老机构
  'SH02YL01': { name: '上海浦东阳光养老院', type: 'NURSING_HOME', level: '机构级' },
  'SH02YL02': { name: '上海浦东康复养老院', type: 'NURSING_HOME', level: '机构级' },
  'SH03YL01': { name: '上海徐汇长者之家', type: 'NURSING_HOME', level: '机构级' },
  'BJ02YL01': { name: '北京朝阳康养中心', type: 'NURSING_HOME', level: '机构级' },
  
  // 医疗机构
  'SH02YY01': { name: '上海浦东人民医院', type: 'HOSPITAL', level: '机构级' },
  'BJ02YY01': { name: '北京朝阳医院', type: 'HOSPITAL', level: '机构级' },
  
  // 兼容原有演示代码
  'platform': { name: '系统平台', type: 'PLATFORM', level: '系统级' },
  'demo_hospital': { name: '演示医院', type: 'HOSPITAL', level: '机构级' },
  'gov_province_civil': { name: '省民政厅', type: 'GOVERNMENT', level: '省级' },
  'gov_city_a_civil': { name: '市民政局A', type: 'GOVERNMENT', level: '市级' },
  'nursing_home_a1': { name: '阳光养老院', type: 'NURSING_HOME', level: '机构级' },
};

// 机构代码提示信息
const tenantCodeHint = ref('');

// 处理机构代码输入（自动转换为大写并提供提示）
const handleTenantCodeInput = (value: string) => {
  const upperValue = value.toUpperCase();
  loginForm.tenantCode = upperValue;
  
  // 提供智能提示
  if (upperValue.length >= 2) {
    const matchedTenant = tenantCodeMap[upperValue];
    if (matchedTenant) {
      tenantCodeHint.value = `✓ ${matchedTenant.name} (${matchedTenant.level})`;
    } else {
      // 模糊匹配提示
      const suggestions = Object.keys(tenantCodeMap).filter(code => 
        code.startsWith(upperValue)
      ).slice(0, 3);
      
      if (suggestions.length > 0) {
        const suggestionText = suggestions.map(code => 
          `${code} (${tenantCodeMap[code].name})`
        ).join(', ');
        tenantCodeHint.value = `💡 建议: ${suggestionText}`;
      } else {
        tenantCodeHint.value = '⚠️ 机构代码不存在，请检查输入';
      }
    }
  } else {
    tenantCodeHint.value = '';
  }
};

// 验证机构代码是否有效 (预留功能)
// const validateTenantCode = (tenantCode: string): boolean => {
//   return tenantCodeMap.hasOwnProperty(tenantCode.toUpperCase());
// };

// 获取机构信息 (预留功能)
// const getTenantInfo = (tenantCode: string): TenantInfo | null => {
//   const upperCode = tenantCode.toUpperCase();
//   return tenantCodeMap[upperCode] || null;
// };

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return;

  try {
    const valid = await loginFormRef.value.validate();
    if (!valid) return;

    // 检查验证码是否已通过
    if (!captchaVerified.value) {
      ElMessage.error('请先完成滑动验证码');
      return;
    }

    loading.value = true;

    // 准备登录数据
    const loginData: any = {
      ...loginForm
    };

    // 如果有验证码，添加验证码信息
    if (captchaToken.value && captchaVerification.value) {
      loginData.captchaToken = captchaToken.value;
      loginData.captchaVerification = captchaVerification.value;
    }

    const response: any = await request({
      url: '/api/auth/login',
      method: 'post',
      data: loginData,
    });

    console.log('Login response:', response);
    console.log('Login response data:', response.data);

    const userData = response.data || response;
    
    if (userData.accessToken) {
      // 登录成功，清除失败次数
      localStorage.removeItem('loginAttempts');
      resetCaptchaState();

      localStorage.setItem('token', userData.accessToken);
      localStorage.setItem('refreshToken', userData.refreshToken || '');
      
      const userInfo = {
        userId: userData.userId,
        username: userData.username,
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        tenantId: userData.tenantId,
        tenantCode: userData.tenantCode,
        tenantName: userData.tenantName,
        tenantRole: userData.tenantRole,
        displayName: userData.displayName,
        permissions: userData.permissions,
        accessibleTenantIds: userData.accessibleTenantIds,
        isActive: userData.active,
        isSuperAdmin: userData.superAdmin,
        platformRole: userData.platformRole
      };
      
      localStorage.setItem('user', JSON.stringify(userInfo));

      // 显示登录成功信息和权限范围
      if (userData.superAdmin) {
        ElMessage.success('🔧 系统总管理员登录成功！拥有平台最高权限');
      } else {
        const hierarchyInfo = userData.hierarchyDescription || '仅访问当前机构数据';
        ElMessage.success(`🏥 ${userData.tenantName} 登录成功！${hierarchyInfo}`);
      }

      router.push('/');
    } else {
      ElMessage.error(userData.message || '登录失败');
    }
  } catch (error: any) {
    console.error('Login error:', error);
    
    // 记录登录失败次数
    const currentAttempts = parseInt(localStorage.getItem('loginAttempts') || '0');
    localStorage.setItem('loginAttempts', String(currentAttempts + 1));
    
    let errorMessage = '登录失败';
    if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error.response?.data) {
      errorMessage = typeof error.response.data === 'string' 
        ? error.response.data 
        : JSON.stringify(error.response.data);
    } else if (error.message) {
      errorMessage = error.message;
    } else {
      errorMessage = '网络连接异常，请检查网络状态';
    }
    
    // 如果是验证码相关错误，清除验证码状态
    if (errorMessage.includes('验证码')) {
      resetCaptchaState();
    }
    
    ElMessage.error(errorMessage);
  } finally {
    loading.value = false;
  }
};

// 初始化验证码
const initCaptcha = async () => {
  try {
    console.log('开始加载验证码...');
    const response = await getCaptcha();
    console.log('验证码响应:', response);
    if (response.success && response.data) {
      Object.assign(captchaData, response.data);
      console.log('验证码数据已更新:', captchaData);
      resetSlider();
    } else {
      console.error('验证码加载失败 - 响应格式错误:', response);
      showTip('验证码加载失败', false);
    }
  } catch (error) {
    console.error('获取验证码失败:', error);
    showTip('验证码加载失败', false);
  }
};

// 刷新验证码
const refreshCaptcha = async () => {
  resetCaptchaState();
  await initCaptcha();
};

// 重置验证码状态
const resetCaptchaState = () => {
  Object.assign(captchaData, {
    token: '',
    originalImageBase64: '',
    jigsawImageBase64: '',
    secretKey: '',
    result: false
  });
  resetSlider();
  tipMessage.value = '';
  verifySuccess.value = false;
  captchaVerified.value = false;
};

// 重置滑块状态
const resetSlider = () => {
  sliderLeft.value = 0;
  blockLeft.value = 0;
  isMoving.value = false;
  verifySuccess.value = false;
};

// 鼠标按下
const handleMouseDown = (e: MouseEvent) => {
  if (verifySuccess.value) return;
  
  isMoving.value = true;
  startX.value = e.clientX;
  tipMessage.value = '';
  
  // 添加全局鼠标事件监听
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
  
  // 防止文本选择
  e.preventDefault();
};

// 鼠标移动
const handleMouseMove = (e: MouseEvent) => {
  if (!isMoving.value || verifySuccess.value) return;

  const deltaX = e.clientX - startX.value;
  
  // 计算滑块位置，限制在有效范围内
  const maxSlideDistance = imgWidth - 40; // 滑块宽度40px
  const newSliderLeft = Math.max(0, Math.min(deltaX, maxSlideDistance));
  
  sliderLeft.value = newSliderLeft;
  blockLeft.value = newSliderLeft;
};

// 鼠标松开
const handleMouseUp = async () => {
  if (!isMoving.value || verifySuccess.value) return;
  
  isMoving.value = false;
  
  // 移除全局事件监听
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);

  // 验证滑动距离
  try {
    const verifyData = {
      captchaType: 'blockPuzzle',
      token: captchaData.token,
      pointJson: JSON.stringify({
        x: Math.round(sliderLeft.value),
        y: 5
      }),
      verification: captchaData.secretKey
    };

    const response = await checkCaptcha(verifyData);
    
    if (response.success && response.data && response.data.result) {
      verifySuccess.value = true;
      captchaVerified.value = true;
      captchaToken.value = captchaData.token;
      captchaVerification.value = captchaData.secretKey;
      showTip('验证成功', true);
    } else {
      showTip('验证失败，请重试', false);
      resetSlider();
    }
  } catch (error) {
    console.error('验证失败:', error);
    showTip('验证异常，请重试', false);
    resetSlider();
  }
};

// 显示提示信息
const showTip = (message: string, isSuccess: boolean) => {
  tipMessage.value = message;
  verifySuccess.value = isSuccess;
  
  // 3秒后清除提示
  setTimeout(() => {
    if (!isSuccess) {
      tipMessage.value = '';
    }
  }, 3000);
};

// 组件挂载时初始化验证码
onMounted(() => {
  console.log('LoginView组件已挂载，开始初始化验证码...');
  initCaptcha();
});
</script>

<style scoped>
/* 自定义输入框样式 */
:deep(.custom-input .el-input__wrapper) {
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

:deep(.custom-input .el-input__wrapper:hover) {
  border-color: #5357A0;
  box-shadow: 0 0 0 1px rgba(83, 87, 160, 0.1);
}

:deep(.custom-input .el-input__wrapper.is-focus) {
  border-color: #5357A0;
  box-shadow: 0 0 0 2px rgba(83, 87, 160, 0.1);
}

/* 表单项间距 */
:deep(.el-form-item) {
  margin-bottom: 24px;
}

:deep(.el-form-item:last-child) {
  margin-bottom: 0;
}

/* 自动完成样式 */
.org-suggestion {
  width: 100%;
}

.org-suggestion:hover {
  background-color: #f8fafc;
}

/* 快速登录卡片样式 */
.quick-login-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 验证码状态样式 */
.captcha-status {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 16px;
  margin-bottom: 16px;
  border-radius: 6px;
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  font-size: 14px;
}

.captcha-status.warning {
  background: #fef3c7;
  border-color: #f59e0b;
}

.status-icon {
  margin-right: 8px;
}

.status-text {
  color: #0ea5e9;
}

.captcha-status.warning .status-text {
  color: #f59e0b;
}

/* 禁用按钮样式 */
.disabled-button {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
}

/* 内嵌验证码样式 */
.inline-captcha {
  width: 100%;
  margin: 16px 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.captcha-image-panel {
  position: relative;
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e9ecef;
  background: #f8f9fa;
}

.captcha-bg-image {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.captcha-block {
  position: absolute;
  top: 0;
  z-index: 2;
}

.captcha-block-image {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.85) contrast(0.9);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.captcha-refresh {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 3;
}

.captcha-tip {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  color: #ffffff;
  z-index: 3;
  white-space: nowrap;
}

.tip-success {
  background: #52c41a;
}

.tip-error {
  background: #ff4d4f;
}

.tip-fade-enter-active,
.tip-fade-leave-active {
  transition: opacity 0.3s ease;
}

.tip-fade-enter-from,
.tip-fade-leave-to {
  opacity: 0;
}

.captcha-slider {
  width: 100%;
  height: 40px;
  position: relative;
  background: #f5f5f5;
  border-radius: 20px;
  overflow: hidden;
}

.slider-track {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  overflow: hidden;
}

.slider-track-bg {
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #d9d9d9;
  border-radius: 20px;
}

.slider-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #52c41a, #73d13d);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slider-text {
  font-size: 14px;
  color: #999999;
  user-select: none;
  pointer-events: none;
}

.slider-fill .slider-text {
  color: #ffffff;
}

.slider-button {
  position: absolute;
  top: 0;
  left: 0;
  width: 40px;
  height: 40px;
  background: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 2;
  user-select: none;
}

.slider-button:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.slider-icon {
  font-size: 16px;
  transition: all 0.3s ease;
}

.icon-normal {
  color: #999999;
}

.icon-success {
  color: #52c41a;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-1.md\\:grid-cols-2 {
    grid-template-columns: 1fr;
  }
}
</style>