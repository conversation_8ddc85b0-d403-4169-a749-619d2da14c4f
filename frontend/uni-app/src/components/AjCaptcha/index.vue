<template>
  <view class="captcha-container" v-show="visible">
    <view class="captcha-overlay" @click="handleClose"></view>
    <view class="captcha-panel">
      <!-- 验证码头部 -->
      <view class="captcha-header">
        <text class="captcha-title">安全验证</text>
        <view class="captcha-close" @click="handleClose">
          <text class="close-icon">✕</text>
        </view>
      </view>

      <!-- 验证码内容区域 -->
      <view class="captcha-content">
        <!-- 滑动拼图验证码 -->
        <view v-if="captchaData.originalImageBase64" class="slider-captcha">
          <!-- 背景图片 -->
          <view class="captcha-image-panel" :style="{width: imgWidth + 'px', height: imgHeight + 'px'}">
            <image 
              :src="'data:image/png;base64,' + captchaData.originalImageBase64" 
              class="captcha-bg-image"
              :style="{width: '100%', height: '100%'}"
              mode="aspectFit"
            />
            
            <!-- 滑块图片 -->
            <view 
              class="captcha-block"
              v-if="captchaData.jigsawImageBase64"
              :style="{
                left: blockLeft + 'px',
                top: '0px',
                width: blockWidth + 'px',
                height: imgHeight + 'px'
              }"
            >
              <image 
                :src="'data:image/png;base64,' + captchaData.jigsawImageBase64"
                class="captcha-block-image"
                :style="{width: '100%', height: '100%'}"
                mode="aspectFit"
              />
            </view>

            <!-- 刷新按钮 -->
            <view class="captcha-refresh" @click="refreshCaptcha">
              <text class="refresh-icon">🔄</text>
            </view>

            <!-- 提示信息 -->
            <view v-if="tipMessage" class="captcha-tip" :class="verifySuccess ? 'tip-success' : 'tip-error'">
              <text class="tip-text">{{ tipMessage }}</text>
            </view>
          </view>

          <!-- 滑动条 -->
          <view class="captcha-slider" :style="{width: imgWidth + 'px'}">
            <!-- 滑动轨道 -->
            <view class="slider-track">
              <view class="slider-track-bg">
                <text class="slider-text">{{ sliderText }}</text>
              </view>
              
              <!-- 已滑动区域 -->
              <view 
                class="slider-fill" 
                :style="{
                  width: sliderLeft + 'px',
                  transition: isMoving ? 'none' : 'all 0.3s ease'
                }"
              >
                <text class="slider-text">{{ finishText }}</text>
              </view>
            </view>

            <!-- 滑块 -->
            <view 
              class="slider-button"
              :style="{
                left: sliderLeft + 'px',
                transition: isMoving ? 'none' : 'all 0.3s ease'
              }"
              @touchstart="handleTouchStart"
              @touchmove="handleTouchMove"
              @touchend="handleTouchEnd"
            >
              <text class="slider-icon" :class="verifySuccess ? 'icon-success' : 'icon-normal'">
                {{ verifySuccess ? '✓' : '→' }}
              </text>
            </view>
          </view>
        </view>

        <!-- 加载状态 -->
        <view v-else class="captcha-loading">
          <text class="loading-text">验证码加载中...</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getCaptcha, checkCaptcha } from '@/api/captcha'

export default {
  name: 'AjCaptcha',
  
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      // 验证码数据
      captchaData: {
        token: '',
        originalImageBase64: '',
        jigsawImageBase64: '',
        secretKey: '',
        result: false
      },

      // 图片尺寸
      imgWidth: 310,
      imgHeight: 155,
      blockWidth: 47,

      // 滑块位置
      sliderLeft: 0,
      blockLeft: 0,

      // 交互状态
      isMoving: false,
      startX: 0,
      verifySuccess: false,

      // 文本提示
      sliderText: '向右滑动完成验证',
      finishText: '验证成功',
      tipMessage: '',

      // 验证结果
      isVerified: false
    }
  },

  mounted() {
    if (this.visible) {
      this.initCaptcha()
    }
  },

  watch: {
    visible(newVal) {
      if (newVal) {
        this.initCaptcha()
      } else {
        this.resetCaptcha()
      }
    }
  },

  methods: {
    // 初始化验证码
    async initCaptcha() {
      try {
        const response = await getCaptcha()
        if (response.success && response.data) {
          this.captchaData = response.data
          this.resetSlider()
        } else {
          this.showTip('验证码加载失败', false)
        }
      } catch (error) {
        console.error('获取验证码失败:', error)
        this.showTip('验证码加载失败', false)
      }
    },

    // 刷新验证码
    async refreshCaptcha() {
      this.resetCaptcha()
      await this.initCaptcha()
    },

    // 重置验证码状态
    resetCaptcha() {
      this.captchaData = {
        token: '',
        originalImageBase64: '',
        jigsawImageBase64: '',
        secretKey: '',
        result: false
      }
      this.resetSlider()
      this.tipMessage = ''
      this.verifySuccess = false
      this.isVerified = false
    },

    // 重置滑块状态
    resetSlider() {
      this.sliderLeft = 0
      this.blockLeft = 0
      this.isMoving = false
      this.verifySuccess = false
    },

    // 触摸开始
    handleTouchStart(e) {
      if (this.verifySuccess) return
      
      this.isMoving = true
      this.startX = e.touches[0].clientX
      this.tipMessage = ''
    },

    // 触摸移动
    handleTouchMove(e) {
      if (!this.isMoving || this.verifySuccess) return

      const currentX = e.touches[0].clientX
      const deltaX = currentX - this.startX
      
      // 计算滑块位置，限制在有效范围内
      const maxSlideDistance = this.imgWidth - 40 // 滑块宽度40px
      let newSliderLeft = Math.max(0, Math.min(deltaX, maxSlideDistance))
      
      this.sliderLeft = newSliderLeft
      this.blockLeft = newSliderLeft
    },

    // 触摸结束
    async handleTouchEnd() {
      if (!this.isMoving || this.verifySuccess) return
      
      this.isMoving = false

      // 验证滑动距离
      try {
        const verifyData = {
          captchaType: 'blockPuzzle',
          token: this.captchaData.token,
          pointJson: JSON.stringify({
            x: Math.round(this.sliderLeft),
            y: 5
          }),
          verification: this.captchaData.secretKey
        }

        const response = await checkCaptcha(verifyData)
        
        if (response.success && response.data && response.data.result) {
          this.verifySuccess = true
          this.isVerified = true
          this.showTip('验证成功', true)
          
          // 延迟关闭弹窗并触发成功回调
          setTimeout(() => {
            this.$emit('verify-success', {
              token: this.captchaData.token,
              verification: this.captchaData.secretKey
            })
            this.handleClose()
          }, 1000)
          
        } else {
          this.showTip('验证失败，请重试', false)
          this.resetSlider()
        }
      } catch (error) {
        console.error('验证失败:', error)
        this.showTip('验证异常，请重试', false)
        this.resetSlider()
      }
    },

    // 显示提示信息
    showTip(message, isSuccess) {
      this.tipMessage = message
      this.verifySuccess = isSuccess
      
      // 3秒后清除提示
      setTimeout(() => {
        if (!isSuccess) {
          this.tipMessage = ''
        }
      }, 3000)
    },

    // 关闭验证码弹窗
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
.captcha-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.captcha-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.captcha-panel {
  position: relative;
  width: 360px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.captcha-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.captcha-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}

.captcha-close {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  background: #f1f3f4;
  cursor: pointer;
}

.close-icon {
  font-size: 14px;
  color: #666666;
}

.captcha-content {
  padding: 20px;
}

.slider-captcha {
  width: 100%;
}

.captcha-image-panel {
  position: relative;
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

.captcha-bg-image {
  display: block;
  width: 100%;
  height: 100%;
}

.captcha-block {
  position: absolute;
  top: 0;
  z-index: 2;
}

.captcha-block-image {
  display: block;
  width: 100%;
  height: 100%;
  filter: brightness(0.85) contrast(0.9);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.captcha-refresh {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.refresh-icon {
  font-size: 14px;
  color: #666666;
}

.captcha-tip {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  color: #ffffff;
  z-index: 3;
}

.tip-success {
  background: #52c41a;
}

.tip-error {
  background: #ff4d4f;
}

.tip-text {
  color: #ffffff;
}

.captcha-slider {
  width: 100%;
  height: 40px;
  position: relative;
  background: #f5f5f5;
  border-radius: 20px;
  overflow: hidden;
}

.slider-track {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  overflow: hidden;
}

.slider-track-bg {
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #d9d9d9;
  border-radius: 20px;
}

.slider-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #52c41a, #73d13d);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slider-text {
  font-size: 14px;
  color: #999999;
  user-select: none;
}

.slider-fill .slider-text {
  color: #ffffff;
}

.slider-button {
  position: absolute;
  top: 0;
  left: 0;
  width: 40px;
  height: 40px;
  background: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.slider-icon {
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.icon-normal {
  color: #999999;
}

.icon-success {
  color: #52c41a;
}

.captcha-loading {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-text {
  font-size: 14px;
  color: #999999;
}

/* 响应式设计 */
@media (max-width: 400px) {
  .captcha-panel {
    width: 90%;
    margin: 0 5%;
  }
}
</style>